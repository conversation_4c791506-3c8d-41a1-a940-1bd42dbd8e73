import Provider from 'oidc-provider';
import crypto from 'crypto';
import { CLIENT_ID, CLIENT_SECRET, CONFIG } from './config.js';

// In-memory storage for users and sessions
const users = new Map([
  ['user1', {
    id: 'user1',
    email: '<EMAIL>',
    name: 'Test User 1',
    password: 'password123'
  }],
  ['admin', {
    id: 'admin',
    email: '<EMAIL>',
    name: 'Admin User',
    password: 'admin123'
  }]
]);

const sessions = new Map();
const grants = new Map();

// OIDC Provider configuration
const configuration = {
  clients: [{
    client_id: CLIENT_ID,
    client_secret: CLIENT_SECRET,
    redirect_uris: [CONFIG.REDIRECT_URI],
    grant_types: ['authorization_code', 'refresh_token'],
    response_types: ['code'],
    scope: 'openid profile email'
  }],

  // Add cookies configuration
  cookies: {
    keys: ['some-secret-key-for-development-only']
  },
  
  // Custom adapter for in-memory storage
  adapter: class MemoryAdapter {
    constructor(name) {
      this.name = name;
      this.storage = new Map();
    }

    async upsert(id, payload, expiresIn) {
      const key = `${this.name}:${id}`;
      const data = {
        payload,
        expiresAt: expiresIn ? Date.now() + (expiresIn * 1000) : undefined
      };
      this.storage.set(key, data);
    }

    async find(id) {
      const key = `${this.name}:${id}`;
      const data = this.storage.get(key);
      
      if (!data) return undefined;
      if (data.expiresAt && Date.now() > data.expiresAt) {
        this.storage.delete(key);
        return undefined;
      }
      
      return data.payload;
    }

    async findByUserCode(userCode) {
      for (const [key, data] of this.storage.entries()) {
        if (data.payload?.userCode === userCode) {
          return data.payload;
        }
      }
      return undefined;
    }

    async findByUid(uid) {
      for (const [key, data] of this.storage.entries()) {
        if (data.payload?.uid === uid) {
          return data.payload;
        }
      }
      return undefined;
    }

    async consume(id) {
      const key = `${this.name}:${id}`;
      const data = this.storage.get(key);
      if (data) {
        data.payload.consumed = Math.floor(Date.now() / 1000);
      }
    }

    async destroy(id) {
      const key = `${this.name}:${id}`;
      this.storage.delete(key);
    }

    async revokeByGrantId(grantId) {
      for (const [key, data] of this.storage.entries()) {
        if (data.payload?.grantId === grantId) {
          this.storage.delete(key);
        }
      }
    }
  },

  // Custom claims
  claims: {
    openid: ['sub'],
    profile: ['name', 'email'],
    email: ['email']
  },

  // Features configuration
  features: {
    devInteractions: { enabled: false },
    deviceFlow: { enabled: false },
    introspection: { enabled: true },
    revocation: { enabled: true }
  },

  // TTL configuration
  ttl: {
    AccessToken: 60 * 60, // 1 hour
    AuthorizationCode: 10 * 60, // 10 minutes
    IdToken: 60 * 60, // 1 hour
    RefreshToken: 24 * 60 * 60 // 24 hours
  },

  // Custom interaction handling
  interactions: {
    url(ctx, interaction) {
      return `/interaction/${interaction.uid}`;
    }
  },

  // Custom account finding
  findAccount: async (ctx, id) => {
    const user = users.get(id);
    if (!user) return undefined;

    return {
      accountId: id,
      async claims() {
        return {
          sub: id,
          name: user.name,
          email: user.email
        };
      }
    };
  }
};

export function createProvider() {
  const provider = new Provider(CONFIG.PROVIDER_URL, configuration);

  // Custom interaction routes - only handle interaction paths
  provider.use(async (ctx, next) => {
    // Only handle interaction routes, let other routes pass through
    if (ctx.path.startsWith('/interaction/')) {
      const uid = ctx.path.split('/')[2];

      if (ctx.method === 'GET') {
        // Show login form
        ctx.body = getLoginForm(uid);
        return;
      }

      if (ctx.method === 'POST') {
        // Handle login
        const body = await parseBody(ctx.req);
        const { username, password } = body;

        const user = users.get(username);
        if (user && user.password === password) {
          const interaction = await provider.interactionDetails(ctx.req, ctx.res);
          await provider.interactionFinished(ctx.req, ctx.res, {
            login: { accountId: username }
          });
          return;
        } else {
          ctx.body = getLoginErrorForm(uid);
          return;
        }
      }
    }

    // Continue to next middleware for all other routes
    await next();
  });

  return provider;
}

function getLoginForm(uid) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Login</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 400px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .error { color: red; margin-top: 10px; }
      </style>
    </head>
    <body>
      <h2>Login</h2>
      <form method="post">
        <div class="form-group">
          <label>Username:</label>
          <input type="text" name="username" required>
        </div>
        <div class="form-group">
          <label>Password:</label>
          <input type="password" name="password" required>
        </div>
        <button type="submit">Login</button>
      </form>
      <p><small>Try: user1/password123 or admin/admin123</small></p>
    </body>
    </html>
  `;
}

function getLoginErrorForm(uid) {
  return `
    <!DOCTYPE html>
    <html>
    <body>
      <h2>Login Failed</h2>
      <p>Invalid credentials. <a href="/interaction/${uid}">Try again</a></p>
    </body>
    </html>
  `;
}

// Helper function to parse form body
async function parseBody(req) {
  return new Promise((resolve) => {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      const params = new URLSearchParams(body);
      const result = {};
      for (const [key, value] of params) {
        result[key] = value;
      }
      resolve(result);
    });
  });
}
