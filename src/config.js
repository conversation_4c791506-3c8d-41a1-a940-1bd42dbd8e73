import crypto from 'crypto';

// Fixed client ID for consistency across restarts
// In production, this should be stored in environment variables
export const CLIENT_ID = process.env.CLIENT_ID || 'demo-client-' + crypto.randomBytes(8).toString('hex');

// HMAC secret for generating client secret
const HMAC_SECRET = process.env.HMAC_SECRET || 'your-hmac-secret-key-change-in-production';

// Generate client secret using HMAC
export const CLIENT_SECRET = crypto
  .createHmac('sha256', HMAC_SECRET)
  .update(CLIENT_ID)
  .digest('hex');

// Server configuration
export const CONFIG = {
  PROVIDER_PORT: 3000,
  CLIENT_PORT: 3001,
  PROVIDER_URL: 'http://localhost:3000',
  CLIENT_URL: 'http://localhost:3001',
  REDIRECT_URI: 'http://localhost:3001/callback',
  SCOPES: 'openid profile email'
};

console.log('Generated Client Credentials:');
console.log('CLIENT_ID:', CLIENT_ID);
console.log('CLIENT_SECRET:', CLIENT_SECRET);
