// src/helper/route/index.ts
import { GET_MATCH_RESULT } from "../../request/constants.js";
import { getPattern, splitRoutingPath } from "../../utils/url.js";
var matchedRoutes = (c) => c.req[GET_MATCH_RESULT][0].map(([[, route]]) => route);
var routePath = (c) => matchedRoutes(c)[c.req.routeIndex].path;
var baseRoutePath = (c) => matchedRoutes(c)[c.req.routeIndex].basePath;
var basePathCacheMap = /* @__PURE__ */ new WeakMap();
var basePath = (c) => {
  const routeIndex = c.req.routeIndex;
  const cache = basePathCacheMap.get(c) || [];
  if (typeof cache[routeIndex] === "string") {
    return cache[routeIndex];
  }
  let result;
  const rp = baseRoutePath(c);
  if (!/[:*]/.test(rp)) {
    result = rp;
  } else {
    const paths = splitRoutingPath(rp);
    const reqPath = c.req.path;
    let basePathLength = 0;
    for (let i = 0, len = paths.length; i < len; i++) {
      const pattern = getPattern(paths[i], paths[i + 1]);
      if (pattern) {
        const re = pattern[2] === true || pattern === "*" ? /[^\/]+/ : pattern[2];
        basePathLength += reqPath.substring(basePathLength + 1).match(re)?.[0].length || 0;
      } else {
        basePathLength += paths[i].length;
      }
      basePathLength += 1;
    }
    result = reqPath.substring(0, basePathLength);
  }
  cache[routeIndex] = result;
  basePathCacheMap.set(c, cache);
  return result;
};
export {
  basePath,
  baseRoutePath,
  matchedRoutes,
  routePath
};
